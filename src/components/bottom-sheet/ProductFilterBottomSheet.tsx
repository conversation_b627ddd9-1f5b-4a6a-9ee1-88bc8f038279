import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetBrands from "@/src/services/querys/vendor/useGetBrands";
import useGetCategories from "@/src/services/querys/vendor/useGetCategories";
import { ICategory, IVendorBrand } from "@/src/types";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import React, { useEffect, useMemo, useState } from "react";
import {
  Dimensions,
  Image,
  Keyboard,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import SkeletonBrandList from "../loader/SkeletonBrandList";
import SkeletonCategoryList from "../loader/SkeletonCategoryList";
import { InfiniteScrollList } from "../shared/InfiniteScrollList";
import EmptyBrandListUI from "../ui/EmptyBrandList";
import EmptyCategories from "../ui/EmptyCategories";
import { ErrorComponent } from "../ui/ErrorComponent";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export interface ProductFilterOptions {
  brandId?: string;
  brandName?: string;
  categoryId?: string;
  categoryName?: string;
}

interface ProductFilterBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: ProductFilterOptions) => void;
  currentFilters?: ProductFilterOptions;
}

export default function ProductFilterBottomSheet({
  isVisible,
  onClose,
  onApplyFilters,
  currentFilters,
}: ProductFilterBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const [activeTab, setActiveTab] = useState<"brand" | "category">("brand");
  const [selectedBrand, setSelectedBrand] = useState<{
    _id: string;
    name: string;
  } | null>(
    currentFilters?.brandId
      ? {
          _id: currentFilters.brandId,
          name: currentFilters.brandName || "",
        }
      : null
  );
  const [selectedCategory, setSelectedCategory] = useState<{
    id: string;
    name: string;
  } | null>(
    currentFilters?.categoryId
      ? {
          id: currentFilters.categoryId,
          name: currentFilters.categoryName || "",
        }
      : null
  );

  // Brands query
  const {
    data: brandsData,
    isLoading: brandsLoading,
    isFetchingNextPage: brandsFetchingNextPage,
    hasNextPage: brandsHasNextPage,
    fetchNextPage: brandsFetchNextPage,
    refetch: brandsRefetch,
    isRefetching: brandsRefetching,
    isError: brandsError,
  } = useGetBrands();

  // Categories query
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    isFetchingNextPage: categoriesFetchingNextPage,
    hasNextPage: categoriesHasNextPage,
    fetchNextPage: categoriesFetchNextPage,
    refetch: categoriesRefetch,
    isRefetching: categoriesRefetching,
    isError: categoriesError,
  } = useGetCategories();

  const brands = useMemo(() => {
    if (!brandsData?.pages) return [];
    return brandsData.pages.flatMap((page) => page?.data?.items || []);
  }, [brandsData]);

  const categories = useMemo(() => {
    if (!categoriesData?.pages) return [];
    return categoriesData.pages.flatMap((page) => page?.data?.items || []);
  }, [categoriesData]);

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0, { damping: 50 });
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT, { damping: 50 });
    }
  }, [isVisible]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        translateY.value = withSpring(-100, { damping: 50 });
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        if (isVisible) {
          translateY.value = withSpring(0, { damping: 50 });
        }
      }
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, [isVisible]);

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT, { damping: 50 });
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = Math.max(0, context.value.y + event.translationY);
    })
    .onEnd((event) => {
      if (event.translationY > 100 || event.velocityY > 500) {
        runOnJS(handleClose)();
      } else {
        translateY.value = withSpring(0, { damping: 50 });
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    const opacity = isVisible ? 1 - translateY.value / SCREEN_HEIGHT : 0;
    return {
      opacity: opacity * 0.4,
    };
  });

  const handleBrandSelect = (brand: IVendorBrand) => {
    console.log("Brand selected:", brand.name, brand._id);
    console.log("Current selectedBrand:", selectedBrand);
    const isCurrentlySelected = selectedBrand?.id === brand._id;
    console.log("Is currently selected:", isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log("Deselecting brand");
      setSelectedBrand(null); // Deselect if already selected
    } else {
      console.log("Selecting brand");
      setSelectedBrand({ id: brand._id, name: brand.name });
    }
  };

  const handleCategorySelect = (category: ICategory) => {
    console.log("Category selected:", category.name, category._id);
    console.log("Current selectedCategory:", selectedCategory);
    const isCurrentlySelected = selectedCategory?.id === category._id;
    console.log("Is currently selected:", isCurrentlySelected);

    if (isCurrentlySelected) {
      console.log("Deselecting category");
      setSelectedCategory(null); // Deselect if already selected
    } else {
      console.log("Selecting category");
      setSelectedCategory({ id: category._id, name: category.name });
    }
  };

  const handleClearFilters = () => {
    setSelectedBrand(null);
    setSelectedCategory(null);
  };

  const handleApplyFilters = () => {
    const filters: ProductFilterOptions = {};

    // Include both brand and category if selected
    if (selectedBrand) {
      filters.brandId = selectedBrand.id;
      filters.brandName = selectedBrand.name;
    }

    if (selectedCategory) {
      filters.categoryId = selectedCategory.id;
      filters.categoryName = selectedCategory.name;
    }

    onApplyFilters(filters);
    handleClose();
  };

  const renderBrandItem = ({ item }: { item: IVendorBrand }) => {
    const isSelected = selectedBrand?.id === item._id;
    console.log(
      `Rendering brand ${item.name}: isSelected=${isSelected}, selectedBrand?.id=${selectedBrand?.id}, item._id=${item._id}`
    );

    return (
      <TouchableOpacity
        style={[
          styles.filterItem,
          {
            borderBottomColor:
              Colors[currentTheme ?? "dark"]?.border || "#E9E9E9",
            backgroundColor: isSelected
              ? Colors[currentTheme ?? "dark"].primary + "20"
              : "transparent",
          },
        ]}
        onPress={() => handleBrandSelect(item)}
      >
        <View style={styles.brandInfo}>
          <Image
            source={{ uri: item.logo }}
            style={styles.brandLogo}
            resizeMode="cover"
          />
          <View style={styles.brandDetails}>
            <ThemedText type="bold" size={16}>
              {item.name}
            </ThemedText>
            <ThemedText size={12} style={styles.productCount}>
              {item.numberOfProducts} {t("vendor.labels.products")}
            </ThemedText>
          </View>
        </View>
        {isSelected && (
          <MaterialIcons
            name="check-circle"
            size={scaleFont(20)}
            color={Colors[currentTheme ?? "dark"].primary}
          />
        )}
      </TouchableOpacity>
    );
  };

  const renderCategoryItem = ({ item }: { item: ICategory }) => {
    const isSelected = selectedCategory?.id === item._id;
    console.log(
      `Rendering category ${item.name}: isSelected=${isSelected}, selectedCategory?.id=${selectedCategory?.id}, item._id=${item._id}`
    );

    return (
      <TouchableOpacity
        style={[
          styles.filterItem,
          {
            borderBottomColor:
              Colors[currentTheme ?? "dark"]?.border || "#E9E9E9",
            backgroundColor: isSelected
              ? Colors[currentTheme ?? "dark"].primary + "20"
              : "transparent",
          },
        ]}
        onPress={() => handleCategorySelect(item)}
      >
        <View>
          <ThemedText type="bold" size={16}>
            {item.name}
          </ThemedText>
          {item.subcategories && item.subcategories.length > 0 && (
            <ThemedText size={12} style={styles.subcategoriesText}>
              {item.subcategories.slice(0, 3).join(", ")}
              {item.subcategories.length > 3 && "..."}
            </ThemedText>
          )}
        </View>
        {isSelected && (
          <MaterialIcons
            name="check-circle"
            size={scaleFont(20)}
            color={Colors[currentTheme ?? "dark"].primary}
          />
        )}
      </TouchableOpacity>
    );
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <Animated.View style={[styles.overlay, overlayStyle]}>
        <Pressable
          style={StyleSheet.absoluteFillObject}
          onPress={handleClose}
        />
      </Animated.View>

      {/* Bottom Sheet */}
      <GestureDetector gesture={gesture}>
        <Animated.View
          style={[
            styles.bottomsheet_container,
            reanimatedBottomStyle,
            {
              backgroundColor: Colors[currentTheme ?? "dark"].background,
            },
          ]}
        >
          <View style={styles.line} />

          {/* Header */}
          <View style={styles.header}>
            <ThemedText type="bold" size={20}>
              {t("vendor.labels.filterProducts")}
            </ThemedText>
          </View>

          {/* Current Selections */}
          {(selectedBrand || selectedCategory) && (
            <View style={styles.currentSelections}>
              <ThemedText
                type="semi-bold"
                size={14}
                style={styles.selectionsTitle}
              >
                {t("vendor.labels.currentSelections")}:
              </ThemedText>
              <View style={styles.selectionTags}>
                {selectedBrand && (
                  <View
                    style={[
                      styles.selectionTag,
                      {
                        backgroundColor:
                          Colors[currentTheme ?? "dark"].primary + "20",
                      },
                    ]}
                  >
                    <ThemedText
                      size={12}
                      style={{ color: Colors[currentTheme ?? "dark"].primary }}
                    >
                      {selectedBrand.name}
                    </ThemedText>
                    <TouchableOpacity
                      onPress={() => setSelectedBrand(null)}
                      style={styles.removeTag}
                    >
                      <Ionicons
                        name="close"
                        size={14}
                        color={Colors[currentTheme ?? "dark"].primary}
                      />
                    </TouchableOpacity>
                  </View>
                )}
                {selectedCategory && (
                  <View
                    style={[
                      styles.selectionTag,
                      {
                        backgroundColor:
                          Colors[currentTheme ?? "dark"].primary + "20",
                      },
                    ]}
                  >
                    <ThemedText
                      size={12}
                      style={{ color: Colors[currentTheme ?? "dark"].primary }}
                    >
                      {selectedCategory.name}
                    </ThemedText>
                    <TouchableOpacity
                      onPress={() => setSelectedCategory(null)}
                      style={styles.removeTag}
                    >
                      <Ionicons
                        name="close"
                        size={14}
                        color={Colors[currentTheme ?? "dark"].primary}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </View>
          )}

          {/* Tab Selector */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tab,
                {
                  backgroundColor:
                    activeTab === "brand"
                      ? Colors[currentTheme ?? "dark"].primary
                      : "transparent",
                  borderColor: Colors[currentTheme ?? "dark"].border,
                },
              ]}
              onPress={() => setActiveTab("brand")}
            >
              <ThemedText
                type={activeTab === "brand" ? "bold" : "regular"}
                size={14}
                style={{
                  color:
                    activeTab === "brand"
                      ? "#fff"
                      : Colors[currentTheme ?? "dark"].text,
                }}
              >
                {t("vendor.labels.filterByBrand")}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                {
                  backgroundColor:
                    activeTab === "category"
                      ? Colors[currentTheme ?? "dark"].primary
                      : "transparent",
                  borderColor: Colors[currentTheme ?? "dark"].border,
                },
              ]}
              onPress={() => setActiveTab("category")}
            >
              <ThemedText
                type={activeTab === "category" ? "bold" : "regular"}
                size={14}
                style={{
                  color:
                    activeTab === "category"
                      ? "#fff"
                      : Colors[currentTheme ?? "dark"].text,
                }}
              >
                {t("vendor.labels.filterByCategory")}
              </ThemedText>
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            {activeTab === "brand" ? (
              brandsError ? (
                <ErrorComponent
                  error={t("backend.server_error")}
                  onRetry={brandsRefetch}
                />
              ) : (
                <InfiniteScrollList
                  data={brands}
                  renderItem={renderBrandItem}
                  keyExtractor={(item) => item._id}
                  estimatedItemSize={80}
                  isLoading={brandsLoading}
                  isFetchingNextPage={brandsFetchingNextPage}
                  hasNextPage={brandsHasNextPage}
                  fetchNextPage={brandsFetchNextPage}
                  refreshing={brandsRefetching}
                  onRefresh={brandsRefetch}
                  skeletonComponent={<SkeletonBrandList />}
                  emptyComponent={<EmptyBrandListUI />}
                  contentContainerStyle={{ paddingBottom: scale(20) }}
                  showsVerticalScrollIndicator={false}
                  count={5}
                />
              )
            ) : categoriesError ? (
              <ErrorComponent
                error={t("backend.server_error")}
                onRetry={categoriesRefetch}
              />
            ) : (
              <InfiniteScrollList
                data={categories}
                renderItem={renderCategoryItem}
                keyExtractor={(item) => item._id}
                estimatedItemSize={80}
                isLoading={categoriesLoading}
                isFetchingNextPage={categoriesFetchingNextPage}
                hasNextPage={categoriesHasNextPage}
                fetchNextPage={categoriesFetchNextPage}
                refreshing={categoriesRefetching}
                onRefresh={categoriesRefetch}
                skeletonComponent={<SkeletonCategoryList />}
                emptyComponent={<EmptyCategories />}
                contentContainerStyle={{ paddingBottom: scale(20) }}
                showsVerticalScrollIndicator={false}
                count={5}
              />
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <View style={styles.clearButton}>
              <TouchableOpacity
                style={[
                  styles.outlineButton,
                  {
                    borderColor: Colors[currentTheme ?? "dark"].border,
                  },
                ]}
                onPress={handleClearFilters}
                activeOpacity={0.8}
              >
                <ThemedText size={16}>
                  {t("vendor.labels.clearFilters")}
                </ThemedText>
              </TouchableOpacity>
            </View>

            <DefaultButton
              title={t("vendor.labels.applyFilters")}
              onPress={handleApplyFilters}
              style={styles.applyButton}
            />
          </View>

          {/* Close Button */}
          <Pressable onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={scaleFont(24)} />
          </Pressable>
        </Animated.View>
      </GestureDetector>
    </>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "black",
    zIndex: 1000,
  },
  bottomsheet_container: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: SCREEN_HEIGHT * 0.8,
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
    zIndex: 1001,
    paddingHorizontal: scale(20),
  },
  line: {
    width: scale(40),
    height: scale(4),
    backgroundColor: "#E0E0E0",
    alignSelf: "center",
    marginTop: scale(8),
    borderRadius: scale(2),
  },
  header: {
    paddingVertical: scale(20),
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(10),
    height: scale(40),
    width: scale(40),
    borderRadius: scale(20),
    backgroundColor: "rgba(0,0,0,0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  tabContainer: {
    flexDirection: "row",
    marginBottom: scale(20),
    borderRadius: scale(8),
    overflow: "hidden",
  },
  tab: {
    flex: 1,
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    alignItems: "center",
    borderWidth: 1,
  },
  content: {
    flex: 1,
    marginBottom: scale(20),
  },
  filterItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: scale(16),
    paddingHorizontal: scale(4),
    borderBottomWidth: 1,
  },
  brandInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  brandLogo: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    marginRight: scale(12),
  },
  brandDetails: {
    flex: 1,
  },
  productCount: {
    marginTop: scale(2),
    opacity: 0.7,
  },
  subcategoriesText: {
    marginTop: scale(4),
    opacity: 0.7,
  },
  actionButtons: {
    flexDirection: "row",
    gap: scale(12),
    paddingBottom: scale(20),
  },
  clearButton: {
    flex: 1,
  },
  outlineButton: {
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderWidth: 1,
    borderRadius: scale(8),
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  applyButton: {
    flex: 1,
  },
  currentSelections: {
    marginBottom: scale(16),
    padding: scale(12),
    borderRadius: scale(8),
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  selectionsTitle: {
    marginBottom: scale(8),
  },
  selectionTags: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: scale(8),
  },
  selectionTag: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: scale(12),
    paddingVertical: scale(6),
    borderRadius: scale(16),
    gap: scale(6),
  },
  removeTag: {
    padding: scale(2),
  },
});
