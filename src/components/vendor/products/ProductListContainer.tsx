import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetProducts from "@/src/services/querys/vendor/useGetProducts";
import { IVendorProduct } from "@/src/types";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import ProductsHeader from "../../header/ProductHeader";
import SkeletonProductList from "../../loader/SkeletonProductList";
import EmptyProductList from "../../ui/EmptyProductList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import ProductItem from "./ProductItem";

const ProductListContainer = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetProducts();

  const [refreshing, setRefreshing] = useState(false);

  const products = useMemo(() => {
    return (
      data?.pages.flatMap((page) =>
        page.data.items.map((product: IVendorProduct) => ({
          id: product._id,
          name: product.name,
          brand: product.brand?.name || t("vendor.labels.noBrand"),
          image: { uri: product.images[0] },
        }))
      ) || []
    );
  }, [data?.pages]);
  const handleRefetch = async () => await refetch();
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleAddProduct = () => {
    router.navigate(`/(vendor)/(screens)/product/add-product`);
  };

  const handleProductPress = (productId: string) => {
    router.navigate(`/(vendor)/(screens)/product/${productId}/product-details`);
  };

  if (error) {
    return (
      <ErrorComponent
        error={t("vendor.errors.somethingWentWrong")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <>
      <ProductsHeader />
      <View style={styles.productsSection}>
        <InfiniteScrollList
          data={products}
          isLoading={isLoading}
          isFetchingNextPage={isFetchingNextPage}
          hasNextPage={hasNextPage}
          fetchNextPage={fetchNextPage}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          estimatedItemSize={82}
          renderItem={({ item }) => (
            <ProductItem item={item} onPress={handleProductPress} />
          )}
          skeletonComponent={<SkeletonProductList />}
          emptyComponent={<EmptyProductList />}
          count={6}
        />
      </View>

      <TouchableOpacity
        style={[
          styles.fab,
          { backgroundColor: Colors[currentTheme ?? "dark"].primary },
        ]}
        onPress={handleAddProduct}
        activeOpacity={0.8}
      >
        <MaterialIcons name="add" size={28} color="#fff" />
      </TouchableOpacity>
    </>
  );
};

export default ProductListContainer;

const styles = StyleSheet.create({
  productsSection: {
    flex: 1,
  },
  fab: {
    position: "absolute",
    bottom: scale(30),
    right: scale(20),
    width: scale(56),
    height: scale(56),
    borderRadius: scale(28),
    justifyContent: "center",
    alignItems: "center",
    elevation: 8, // elevation should be an integer
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
});
