{"theme": {"title": "Theme Settings", "subtitle": "Choose your preferred app theme", "light": "Light Mode", "dark": "Dark Mode", "system": "System Default", "lightDescription": "Classic light theme for better visibility in daylight", "darkDescription": "Easy on the eyes, perfect for nighttime use", "systemDescription": "Automatically matches your device theme", "note": "Note: Theme changes will apply immediately"}, "language": {"title": "Language Settings", "subtitle": "Choose your preferred language", "note": "Changes will be applied immediately", "system": "System Default"}, "emailSupport": {"title": "How can we help?", "subtitle": "We're here to assist you with any questions or concerns. Please choose the best way to reach us:", "contactForm": {"title": "Contact Form", "description": "Fill out our contact form for a quick response."}, "emailUs": {"title": "Email Us", "description": "Send us an email directly for detailed inquiries."}}, "callSupport": {"title": "Contact us", "subtitle": "Call us at the number below. We're available Monday to Friday, 9 AM to 5 PM."}, "loginIssue": {"title": "Trouble logging in?", "subtitle": "If you're having trouble logging in, here are some common issues and troubleshooting steps:", "commonIssues": "Common Issues", "troubleshootingSteps": "Troubleshooting Steps", "issues": {"incorrectEmail": {"title": "Incorrect Email", "description": "Ensure you're using the correct email address associated with your account."}, "incorrectPassword": {"title": "Incorrect Password", "description": "Double-check your password for typos or incorrect capitalization."}, "forgotPassword": {"title": "Forgot Password", "description": "If you've forgotten your password, use the 'Forgot Password' option to reset it."}}, "steps": {"clearCache": {"title": "Clear App Cache", "description": "Clear the app's cache and data in your device settings."}, "reinstall": {"title": "Reinstall App", "description": "Uninstall and reinstall the app from your device's app store."}, "updateOS": {"title": "Update Device OS", "description": "Ensure your device's operating system is up to date."}}}, "help": {"sections": {"authentication": "Authentication", "contactUs": "Contact Us", "settings": "Settings"}, "options": {"loginIssues": "Login Issues", "signupIssues": "Signup Issues", "passwordRecovery": "Password Recovery", "emailSupport": "Email Support", "callSupport": "Call Support", "language": "Language", "theme": "Theme"}}, "layouts": {"help": "Help", "callSupport": "Call Support", "emailSupport": "Email Support", "loginIssue": "Login Issues", "passwordRecovery": "Password Recovery", "signupIssue": "Signup Issues", "language": "Language", "theme": "Theme"}, "signupHelp": {"title": "Signup Issue?", "intro": "If you're having trouble signing up, here are some common issues and troubleshooting steps:", "commonIssuesTitle": "Common Issues", "invalidEmailTitle": "In<PERSON>id Email Address", "invalidEmailDesc": "Make sure your email address is correctly entered and not already associated with an existing account.", "passwordReqTitle": "Password Requirements", "passwordReqDesc": "Ensure your password meets the required criteria (e.g., length, special characters).", "incorrectPasswordTitle": "Incorrect Password", "incorrectPasswordDesc": "Make sure you're using the correct email address associated with your account.", "existingAccountTitle": "Existing Account", "existingAccountDesc": "If you've already signed up, try logging in or use the 'Forgot Password' option.", "troubleshootingTitle": "Troubleshooting Steps", "verifyCredsTitle": "Verify Credentials", "verifyCredsDesc": "Carefully check your email and password for typos or errors.", "socialMediaTitle": "Social Media Signup", "socialMediaDesc": "If you're signing up using a social media account, ensure it's properly linked and authorized.", "differentDeviceTitle": "Try a Different Device", "differentDeviceDesc": "Try signing up on a different device or browser to rule out device-specific issues.", "contactSupportTitle": "Contact Support", "contactSupportDesc": "If the issue persists, contact our support team for assistance."}, "authentication": {"title": "Authentication", "accountCreation": {"title": "Account Creation", "howToCreate": "How do I create an account?", "createAccountDescription": "To create an account, tap 'Sign Up' on the welcome screen and follow the prompts. You'll need to provide your email address and create a secure password.", "incorrectEmail": "Incorrect Email", "incorrectEmailDescription": "Ensure you're using the correct email address associated with your account.", "troubleCreatingAccount": "I'm having trouble creating an account.", "troubleCreatingAccountDescription": "If you encounter issues during account creation, ensure your email address is valid and not already in use. Check for any error messages and follow the instructions provided."}, "login": {"title": "<PERSON><PERSON>", "howToLogin": "How do I log in?", "loginDescription": "To log in, tap 'Log In' on the welcome screen and enter your registered email address and password. If you've forgotten your password, tap 'Forgot Password' to reset it.", "troubleLoggingIn": "I'm having trouble logging in.", "troubleLoggingInDescription": "If you're having trouble logging in, double-check your email address and password for accuracy. If you've forgotten your password, use the 'Forgot Password' option to reset it.", "resetPasswordQuestion": "How do I reset my password?", "resetPasswordDescription": "To reset your password, tap 'Forgot Password' on the login screen. Enter your email address, and we'll send you an OTP to reset your password. Follow the instructions in the email to create a new password.", "didNotReceiveEmail": "I didn't receive the password reset email.", "didNotReceiveEmailDescription": "If you don't receive the password reset email, check your spam or junk folder. Ensure you've entered the correct email address associated with your account. If you still haven't received it, contact our support team for assistance."}}, "homepage": {"discoverLocalTreasures": "Discover Local Treasures", "connectWithLocalArtisans": "Connect with local artisans and brands, and find unique items in your community.", "logIn": "Log In", "signUp": "Sign Up", "byContinuingAgree": "By continuing, you agree to our", "termsOfService": "Terms of Service and", "privacyPolicy": "Privacy Policy"}, "onboarding": {"slide": {"title": "Discover Local Brands", "description": "Find unique local stores around you with ease."}, "slide-two": {"title": "Explore Products", "description": "Browse a wide variety of authentic products."}, "slide-three": {"title": "Enjoy Seamless Shopping", "description": "Smooth experience across mobile and web."}, "getStarted": "Get Started", "next": "Next"}, "two": {"accountTypePrompt": "Choose your account type"}, "User": "User", "auth": {"userTypes": {"client": "User", "vendor": "<PERSON><PERSON><PERSON>"}, "forgotPassword": {"email": {"label": "Email", "placeholder": "Enter your email address"}, "resetButton": "Reset Password", "title": "Forget Password", "description": "Enter the email associated with your account and we'll send you instructions to reset your password."}, "verifyEmail": {"title": "Verify your email address", "description_parts": {"before": "A message took flight, gentle and bright, to", "after": "Check your inbox — it waits in the light."}}, "resetPassword": {"title": "Reset your password", "description_parts": {"before": "We have sent a reset OTP to", "after": "Please check your inbox."}, "enterOtp": "Enter the code received on your phone", "enterOtpEmail": "Enter the code received on your email", "changePassword": "Change your password", "theOTP": "The OTP is 4 digits long", "noOtp": "Didn't receive an OTP?", "resend": "Resend", "password": {"label": "Password", "placeholder": "Enter your password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Enter your password again"}, "submit": "Submit"}, "register": {"title": "Create Account", "label": {"placeholder": "Full Name"}, "email": {"placeholder": "Enter your email"}, "password": {"label": "Password", "placeholder": "Enter your password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Re-enter your password"}, "registerButton": "Sign Up", "haveAccount": "Already have an account?", "login": "Sign in"}, "login": {"title": "Sign In", "email": {"placeholder": "Enter your email"}, "password": {"placeholder": "Enter your password"}, "loginButton": "Log In", "noAccount": "Don't have an account?", "signUp": "Sign up", "forgetPasswordLink": "Forgot Password ?"}}, "validation": {"required": {"otp": "OTP code is required", "email": "Email is required", "password": "Password is required", "confirmPassword": "Password confirmation is required", "fullName": "Full name is required", "phone": "Phone number is required", "code": "Code is required", "room": "Room is required", "message": "Message is required", "comment": "Comment is required", "rating": "Rating is required", "brandName": "Brand name is required", "productName": "Product name is required", "description": "Description is required", "logo": "Logo is required", "address": "Address is required", "latitude": "Latitude is required", "longitude": "Longitude is required", "city": "City is required", "state": "State is required", "country": "Country is required", "zipCode": "Zip code is required", "location": "Location is required", "category": "Category is required", "brand": "Brand is required", "price": "Price is required", "images": "Images are required"}, "email": {"invalid": "Please enter a valid email address"}, "phone": {"invalid": "Please enter a valid phone number"}, "fullName": {"min": "Full name must be at least 2 characters", "max": "Full name must not exceed 50 characters"}, "room": {"min": "Room name must be at least 3 characters"}, "message": {"min": "Message cannot be empty", "max": "Message must not exceed 500 characters"}, "comment": {"min": "Comment cannot be empty", "max": "Comment must not exceed 500 characters"}, "rating": {"min": "Rating must be at least 1", "max": "Rating must be at most 5"}, "brandName": {"min": "Brand name must be at least 2 characters"}, "productName": {"min": "Product name must be at least 2 characters"}, "description": {"min": "Description must be at least 10 characters"}, "address": {"min": "Address must be at least 5 characters"}, "city": {"min": "City must be at least 2 characters"}, "state": {"min": "State must be at least 2 characters"}, "country": {"min": "Country must be at least 2 characters"}, "zipCode": {"min": "Zip code must be at least 3 characters"}, "category": {"min": "Category must be at least 2 characters"}, "price": {"typeError": "Price must be a number", "min": "Price cannot be negative"}, "images": {"min": "At least one image is required", "max": "Maximum of six images allowed"}, "min": "{{field}} must be at least {{min}} characters", "max": "{{field}} must not exceed {{max}} characters", "matches": "{{field}} must match {{match}}", "code": {"required": "Code is required", "length": "Code must be exactly 4 digits"}, "password": {"min": "Password must be at least 8 characters", "matches": "Passwords must match", "requirements": "Password must contain uppercase, lowercase, number and special character", "strength": {"label": "Strength :", "weak": " Weak", "fair": " Fair", "good": " Medium", "strong": " Strong"}}, "otp": {"required": "OTP code is required", "length": "OTP must be exactly 4 digits"}, "location": {"required": "Location is required"}}, "error": {"notFound": {"title": "This screen doesn't exist.", "message": "Go back to home page?"}, "generic": {"title": "Oops! Something went wrong", "message": "Please try again.", "retry": "Try Again"}, "network": {"title": "Connection Error", "message": "Please check your internet connection and try again.", "retry": "Retry"}}, "backend": {"account_type_mismatch": "Account type mismatch", "invalid_account_type": "Invalid account type", "yourAccountDeleted": "Your account has been deleted successfully", "vendor_not_found": "<PERSON><PERSON><PERSON> not found", "cannot_delete_vendor_with_active_orders": "Cannot delete vendor with active orders", "bad_request": "Bad request", "internal_server_error": "Internal server error", "validation_error": "Validation error", "unexpected_field_detectted_in_request_body": "Unexpected field detected in request body", "unexpected_field_detected": "Unexpected field detected", "email_required": "Email is required", "email_invalid": "<PERSON><PERSON> is invalid", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters long", "name_required": "Name is required", "name_min_length": "Name must be at least 3 characters long", "name_max_length": "Name must be at most 50 characters long", "type_required": "Type is required", "type_invalid": "Type must be either 'client' or 'vendor'", "otp_required": "OTP is required", "otp_invalid_length": "OTP must be exactly 4 characters long", "otp_type_required": "OTP type is required", "otp_type_invalid": "OTP type must be either 'reset-password' or 'created-account'", "new_password_required": "New password is required", "new_password_min_length": "New password must be at least 8 characters long", "userNotFound": "User not found", "loginSuccess": "Login successful", "invalidCredentials": "Invalid credentials", "blockedLogin": "Account is blocked", "accountNotVerified": "Account is not verified", "emailNotExist": "Email does not exist", "emailExists": "Email already exists", "registrationSuccess": "Registration successful", "otpEmailFailed": "Failed to send OTP email", "serverError": "Server error", "brand_name_required": "Brand name is required", "brand_description_must_be_string": "Brand description must be a string", "brand_email_invalid": "Brand email is invalid", "brand_phone_must_be_string": "Brand phone must be a string", "location_address_required": "Location address is required", "location_latitude_must_be_number": "Location latitude must be a number", "location_longitude_must_be_number": "Location longitude must be a number", "location_city_required": "Location city is required", "location_state_required": "Location state is required", "location_country_required": "Location country is required", "location_zipcode_required": "Location zip code is required", "location_must_be_object": "Location must be an object", "location_required": "Location is required", "brand_category_required": "Brand category is required", "product_name_required": "Product name is required", "product_description_must_be_string": "Product description must be a string", "product_price_required": "Product price is required", "product_price_must_be_positive": "Product price must be a positive number", "product_category_required": "Product category is required", "product_category_invalid": "Product category is invalid", "product_brand_invalid": "Product brand is invalid", "product_images_must_be_array": "Product images must be an array", "order_status_required": "Order status is required", "order_status_invalid": "Order status must be one of: Pending, Accepted, Delivered, Cancelled", "access_token_missing_or_invalid": "Access token is missing or invalid", "invalid_or_expired_token": "Invalid or expired token", "brand_created": "Brand created successfully", "failed_to_create_brand": "Failed to create brand", "brand_not_found": "Brand not found", "brand_found": "Brand found", "failed_to_fetch_brand": "Failed to fetch brand", "brand_updated": "Brand updated successfully", "failed_to_update_brand": "Failed to update brand", "brands_found": "Brands found", "failed_to_fetch_brands": "Failed to fetch brands", "product_created": "Product created successfully", "failed_to_create_product": "Failed to create product", "product_not_found": "Product not found", "product_found": "Product found", "failed_to_fetch_product": "Failed to fetch product", "product_updated": "Product updated successfully", "failed_to_update_product": "Failed to update product", "products_found": "Products found", "failed_to_fetch_products": "Failed to fetch products", "invalid_brand": "Invalid brand", "categories_found": "Categories found", "failed_to_fetch_categories": "Failed to fetch categories", "orders_found": "Orders found", "failed_to_fetch_orders": "Failed to fetch orders", "order_found": "Order found", "failed_to_fetch_order": "Failed to fetch order", "order_status_updated": "Order status updated successfully", "failed_to_update_order_status": "Failed to update order status", "failed_to_fetch_dashboard_analytics": "Failed to fetch dashboard analytics", "item_added_to_wishlist": "Item added to wishlist", "failed_to_add_to_wishlist": "Failed to add to wishlist", "client_not_found": "Client not found", "item_removed_from_wishlist": "Item removed from wishlist", "failed_to_remove_from_wishlist": "Failed to remove from wishlist", "location_added": "Location added successfully", "failed_to_add_location": "Failed to add location", "locations_found": "Locations found", "failed_to_fetch_locations": "Failed to fetch locations", "location_updated": "Location updated successfully", "failed_to_update_location": "Failed to update location", "location_deleted": "Location deleted successfully", "failed_to_delete_location": "Failed to delete location", "notification_not_found": "Notification not found", "notification_marked_as_read": "Notification marked as read", "failed_to_mark_notification": "Failed to mark notification as read", "notifications_found": "Notifications found", "failed_to_fetch_notifications": "Failed to fetch notifications", "review_added": "Review added successfully", "failed_to_add_review": "Failed to add review", "reviews_found": "Reviews found", "failed_to_fetch_reviews": "Failed to fetch reviews", "search_failed": "Search failed", "search_results_found": "Search results found", "order_products_required": "Order products are required", "brand_required_for_order": "Brand is required for order", "location_required_for_order": "Location is required for order", "invalid_product_in_order": "Invalid product in order", "total_price_mismatch": "Total price does not match calculated total", "order_created": "Order created successfully", "failed_to_create_order": "Failed to create order", "order_not_found": "Order not found", "order_cannot_be_cancelled": "Order cannot be cancelled", "order_cancelled": "Order cancelled successfully", "failed_to_cancel_order": "Failed to cancel order", "invalid_service_id": "Invalid service ID", "profile_not_found": "Profile not found", "service_retrieved_successfully": "Service retrieved successfully", "get_profile_failed": "Failed to get profile", "expo_push_token_not_found": "Expo push token not found", "no_expo_push_tokens_found": "No expo push tokens found", "expo_push_tokens_updated": "Expo push tokens updated", "error_updating_expo_push_tokens": "Error updating expo push tokens", "server_error": "Server error", "new_password_and_confirm_password_do_not_match": "New password and confirm password do not match", "old_password_is_required": "Old password is required", "user_not_found": "User not found", "old_password_is_incorrect": "Old password is incorrect", "new_password_must_be_different_from_old_password": "New password must be different from old password", "failed_to_change_password": "Failed to change password", "client_or_profile_not_found": "Client or profile not found", "account_deleted_successfully": "Account deleted successfully", "password_changed": "Password changed successfully", "invalid_type_must_be_hotel_client_or_admin": "Invalid type. Must be one of: vendor, client.", "expo_push_token_updated": "Expo Push token updated successfully.", "expo_push_token_already_exists": "Expo Push token already exists.", "expo_push_token_added": "Expo Push token added successfully.", "passwordChanged": "Password Changed", "noData": "No Data Found", "invalidOtp": "Invalid OTP or OTP has expired.", "invalidOtpSimple": "Invalid OTP", "invalidOtpType": "Invalid OTP type.", "pleaseWaitSeconds": "Please wait some seconds before requesting a new OTP.", "unexpectedError": "An unexpected error occurred.", "otp_sent": "OTP has been sent to you email.", "success": "Success", "password_reset": "Password reset successfully", "otp_verified": "OTP verified successfully", "error": "Error", "authentication_required": "Authentication required", "insufficient_permissions": "Insufficient permissions", "account_blocked": "Your account is blocked", "internal_server_error_during_authorization": "Internal server error during authorization", "brandAddedSuccessfully": "Brand added successfully", "brandChangedSuccessfully": "Brand updated successfully"}, "settings": {"title": "Settings", "subtitle": "Manage your account and preferences", "profile": {"title": "Profil", "form": {"fullName": {"label": "Nom complet", "placeholder": "Entrez votre nom complet"}, "email": {"label": "E-mail", "placeholder": "Entrez votre e-mail"}, "phone": {"label": "Téléphone", "placeholder": "Entrez votre numéro de téléphone"}}}, "sections": {"account": {"title": "Account", "profile": {"title": "Profile Settings", "subtitle": "Update your personal information", "form": {"fullName": {"label": "Full Name", "placeholder": "Enter your full name"}, "email": {"label": "Email", "placeholder": "Enter your email"}, "phone": {"label": "Phone Number", "placeholder": "Enter your phone number"}, "address": {"label": "Address", "placeholder": "Enter your address"}}, "saveButton": "Save Changes"}, "notifications": {"title": "Notifications", "subtitle": "Manage your notification preferences"}, "security": {"title": "Security", "subtitle": "Change password and security settings"}, "orders": {"title": "Orders", "subtitle": "View your order history"}, "locations": {"title": "Locations", "subtitle": "Manage your saved locations"}}, "app": {"title": "App Settings", "language": {"title": "Language", "subtitle": "Change app language"}, "theme": {"title": "Theme", "subtitle": "Current: {{mode}}"}}, "support": {"title": "Support", "help": {"title": "Help Center", "subtitle": "Get help and contact support"}, "about": {"title": "About", "subtitle": "App version and information"}}}, "security": {"title": "Security Settings", "changePassword": {"title": "Change Password", "currentPassword": {"label": "Current Password", "placeholder": "Enter current password"}, "newPassword": {"label": "New Password", "placeholder": "Enter new password"}, "confirmPassword": {"label": "Confirm New Password", "placeholder": "Confirm new password"}, "button": "Change Password", "errors": {"mismatch": "New passwords don't match", "length": "Password must be at least 8 characters long"}}, "twoFactor": {"comming": "This feature coming soon", "title": "Two-Factor Authentication", "description": "Add an extra layer of security to your account by enabling two-factor authentication.", "button": "Set Up 2FA"}}}, "common": {"deleteAccount": "Delete Account", "logout": "Logout", "save": "Save", "saving": "Saving...", "delete": {"title": "Delete Account", "message": "Are you sure you want to delete you account?", "cancelButton": "Cancel", "deleteButton": "Delete"}}, "screens": {"headers": {"about": "About", "help": "Help", "language": "Language", "login-history": "Login History", "notifications": "Notifications", "profile": "Profile", "security": "Security", "theme": "Theme", "notification": "Notification", "brands": "Brands", "products": "Products", "brand": "Brand", "product": "Product", "order-details": "Order Details", "brand-details": "Brand Details", "product-details": "Product Details", "add-brand": "Add Brand", "add-product": "Add Product", "edit-brand": "Edit Brand", "edit-product": "Edit Product", "products-brand": "Brand Products", "orders": "Orders", "locations": "Locations"}}, "helpSettings": {"title": "Help Center", "subtitle": "How can we help you today?", "categories": {"support": {"title": "Contact Support", "description": "Get in touch with our support team"}, "documentation": {"title": "Documentation", "description": "Read our detailed documentation"}, "tutorials": {"title": "Video Tutorials", "description": "Watch helpful tutorial videos"}}, "faq": {"title": "Frequently Asked Questions", "items": {"hotelListings": {"question": "How do I find local brands and artisans?", "answer": "You can browse local brands and artisans by using our search feature, exploring categories, or checking out featured brands on the home page. Make sure location services are enabled to see businesses near you."}, "complaints": {"question": "How do I report an issue or make a complaint?", "answer": "If you encounter any issues with orders, products, or services, you can contact our support team directly through the Contact Support option in the Help Center. We'll respond to your inquiry as soon as possible."}, "orders": {"question": "How do I track my orders?", "answer": "You can track your orders by going to the Orders section in your account settings. There you'll see all your order history with current status updates including pending, accepted, delivered, or cancelled orders."}, "account": {"question": "How do I manage my account settings?", "answer": "You can manage your account by going to Setting<PERSON> from the main menu. There you can update your profile, change language and theme preferences, manage notifications, view order history, and access security settings."}, "services": {"question": "What services does Locasa offer?", "answer": "Locasa is a local marketplace platform that connects you with local artisans and brands. You can discover unique products, place orders, manage your favorites, track deliveries, and support local businesses in your community."}}}}, "about": {"version": "Version {{version}} ({{buildNumber}})", "title": "About Us", "description": "Locasa is a comprehensive local marketplace platform designed to connect users with local artisans and brands. Our platform provides powerful tools for discovering unique products and supporting local businesses in your community.", "connect": "Connect With Us", "social": {"website": "Website", "twitter": "Twitter", "instagram": "Instagram"}, "legal": {"title": "Legal", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "licenses": "Licenses"}, "copyright": "© {{year}} Locasa. All rights reserved."}, "notifications": {"title": "Notification Settings", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "bookingUpdates": "Booking Updates", "newMessages": "New Messages", "marketing": "Marketing", "pushNotificationsDescription": "Receive push notifications for important updates", "emailNotificationsDescription": "Receive email notifications for account activities", "bookingUpdatesDescription": "Receive notifications for booking status changes", "newMessagesDescription": "Receive notifications for new messages", "marketingDescription": "Receive promotional offers and updates", "permissionRequired": "Permission Required", "permissionRequiredMessage": "Please enable notification permissions in your device settings to receive push notifications and new message alerts.", "empty": {"title": "You're all caught up!", "description": "You don't have any notifications at the moment. Check back later or explore some local brands!"}, "messages": {"recommendation": "🎯 New product recommended just for you!", "invitation": "🏷️ You're invited to explore a new brand!", "accepted": "📦 Your order has been accepted. Wait for the delivery call!", "favorite_brand": "✨ Check out this new item from your favorite brand!"}}, "navigation": {"tabs": {"home": "Home", "favorites": "Favorites", "shopc": "Shopping Cart", "settings": "Settings", "orders": "Orders", "brands": "Brands", "products": "Products", "dashboard": "Analytics"}}, "dashboard": {"overview": "Overview", "totalOrders": "Total Orders", "delivered": "Delivered", "pending": "Pending", "monthlyOrders": "Monthly Orders", "totalOrdersOverTime": "Total Orders Over Time", "orderStatusDistribution": "Order Status Distribution", "deliveredOrders": "Delivered Orders", "successfullyDeliveredThisPeriod": "Successfully Delivered This Period", "mostOrderedProducts": "Most Ordered Products", "bestPerformingProducts": "Best Performing Products", "orderStatistics": "Order Statistics", "completedOrders": "Completed Orders", "cancelledOrders": "Cancelled Orders", "productPerformance": "Product Performance", "customerEngagement": "Customer Engagement", "activeCustomers": "Active Customers", "newCustomers": "New Customers", "topProducts": "Top 5"}, "notFound": {"title": "Oops! Page Not Found", "description": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "goHome": "Go Home"}, "app": {"name": "Locasa"}, "currency": {"symbol": "TND", "name": "Tunisian Dinar", "format": "{amount} TND"}, "ui": {"emptyBrand": {"title": "No brands yet", "description": "Start by adding your first brand to tell your story and sell your products!", "noFavorites": "No brands found. Please check this section later to add a new brand to your favorites."}}, "location": {"permission": {"title": "Enable Location", "description": "To find local brands and artisans near you, Locasa needs access to your location. Please enable location services in your device settings.", "openSettings": "Open Settings"}}, "buttons": {"help": "Help", "addNewBrand": "Add new brand", "enableLocationAccess": "Enable Location Access", "saveBrand": "Save Brand", "saveProduct": "Save Product", "updateProduct": "Update Product", "cancel": "Cancel", "addToCart": "Add to Cart", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "startShopping": "Start Shopping", "viewProducts": "View Products", "addReview": "Add a review", "viewAll": "View all", "updateBrand": "Update Brand", "openSettings": "Open Settings", "showMore": "Show More", "showLess": "Show Less"}, "search": {"placeholder": "Search for brands & products", "recentlySearched": "Recently searched", "sampleSearches": ["Pizza", "<PERSON><PERSON>", "Burgers", "Pasta"], "result": "result", "results": "results", "found": "found", "noResults": "No results found", "tryDifferentKeywords": "Try searching with different keywords"}, "modal": {"deleteConfirmation": {"defaultTitle": "Delete Confirmation", "defaultMessage": "Are you sure you want to delete this item? This action cannot be undone.", "defaultCancel": "Cancel", "defaultDelete": "Delete"}}, "timestamps": {"minsAgo": "2 mins ago", "hourAgo": "1 hour ago", "today": "Today", "yesterday": "Yesterday"}, "emptyStates": {"orders": {"title": "No Orders yet", "description": "No orders found. Please check this section later "}, "products": {"title": "No Products yet", "description": "No products found. Please check this section later "}, "reviews": {"title": "No Reviews yet", "description": "Be the first to share your experience with Crafted Goods. Your feedback helps others discover quality local products."}, "locations": {"title": "No Locations yet", "description": "Add your first location to manage your saved addresses."}}, "orderStatus": {"pending": "Pending", "accepted": "Accepted", "delivered": "Delivered", "cancelled": "Cancelled"}, "errors": {"orderNotFound": "Order Not Found", "generic": {"title": "Something went wrong", "retry": "Try Again"}}, "orderDetails": {"sections": {"product": "Product", "products": "Products", "customer": "Customer", "delivery": "Delivery", "orderInformation": "Order Information"}, "labels": {"email": "Email", "brand": "Brand", "phone": "Phone", "customerId": "Customer ID", "address": "Address", "locationType": "Location Type", "orderDate": "Order Date", "orderStatus": "Order Status", "totalPrice": "Total Price", "orderId": "Order ID", "unitPrice": "per unit", "totalForItem": "Total for this item"}, "fallbacks": {"unknownProduct": "Unknown Product", "unknownBrand": "Unknown Brand", "unknownCustomer": "Unknown Customer", "phoneNotProvided": "Not provided", "emailNotProvided": "Not provided", "addressNotProvided": "Address not provided", "notAvailable": "N/A", "dataUnavailable": "Data unavailable"}, "actions": {"markAsDelivered": "Delivered", "markAsAccepted": "Accept", "markAsCancelled": "Cancel", "returnToPending": "Return to Pending", "updateStatus": "Update Status"}, "modal": {"cancelTitle": "Cancel Order", "cancelMessage": "Are you sure you want to cancel this order? This action cannot be undone and the order cannot be returned to pending status.", "cancelConfirm": "Cancel Order", "cancelCancel": "Keep Order", "deliveredTitle": "Delivered", "deliveredMessage": "Are you sure you want to mark this order as delivered? This action will notify the customer that their order has been completed.", "deliveredConfirm": "Delivered", "deliveredCancel": "Cancel"}, "status": {"orderDelivered": "Order has been delivered successfully"}}, "cart": {"title": "<PERSON><PERSON>", "item": "item", "items": "items", "viewCart": "View Cart", "clearAll": "Clear All", "subtotal": "Subtotal", "shipping": "Shipping", "total": "Total", "free": "Free", "viewFullCart": "View Full Cart", "labels": {"subtotal": "Subtotal", "shipping": "Shipping", "total": "Total", "free": "Free"}, "actions": {"checkout": "Checkout", "processing": "Processing..."}, "brandValidation": {"title": "Different Brand Detected", "description": "You can only add products from one brand at a time. Your cart currently contains items from a different brand.", "currentBrand": "Current Brand", "newBrand": "New Brand", "suggestion": "You can either clear your cart and add this product, or continue shopping with your current brand.", "clearAndAdd": "Clear Cart & Add Product", "keepShopping": "Keep Current Cart"}, "orderCreated": "Order created successfully!"}, "addresses": {"title": "Your Addresses", "editLocation": "Edit Location", "selectType": "Select Address Type", "types": {"home": "Home", "work": "Work", "other": "Other", "workplace": "Workplace"}, "empty": {"title": "No addresses saved", "description": "Add an address to make checkout faster and easier."}, "actions": {"addAddress": "Add Address", "addNewAddress": "Add New Address"}}, "orders": {"title": "Your Orders", "multipleProducts": "{{count}} Products", "actions": {"cancel": "Cancel Order"}, "modal": {"cancelTitle": "Cancel Order", "cancelMessage": "Are you sure you want to cancel this order? This action cannot be undone.", "cancelConfirm": "Cancel Order", "cancelCancel": "Keep Order"}}, "locations": {"title": "Your Locations", "subtitle": "Manage your saved locations", "empty": {"title": "No locations saved", "description": "Add locations to manage your saved addresses."}, "actions": {"edit": "Edit", "delete": "Delete", "addLocation": "Add Location"}, "labels": {"address": "Address", "city": "City", "state": "State", "country": "Country", "zipCode": "Zip Code", "type": "Type"}}, "map": {"selectLocation": "Select Location", "updateLocation": "Update Location", "searchPlaceholder": "Search for a location...", "selectedLocation": "Selected Location", "selectedAddress": "Selected Address", "confirmLocation": "Confirm Location", "errors": {"noLocationTitle": "No Location Selected", "noLocationMessage": "Please select a location on the map before confirming."}}, "addressType": {"title": "What type of address is this?", "selectedLocation": "Selected Location", "saveAddress": "Save Address", "descriptions": {"home": "Your home address for personal deliveries", "work": "Your workplace address for office deliveries", "other": "Any other address you'd like to save"}}, "share": {"title": "Awesome Content"}, "alerts": {"success": "Success", "error": "Error", "orderMarkedDelivery": "Order marked as out for delivery!", "deleteProductFailed": "Failed to delete product. Please try again.", "deleteOrderFailed": "Failed to delete order. Please try again.", "cancelOrderFailed": "Failed to cancel order. Please try again.", "pushNotificationDevice": "Must use physical device for Push Notifications"}, "vendor": {"headers": {"products": "Products", "productsSubtitle": "Manage your product catalog", "welcomeBack": "Welcome back", "brandsSubtitle": "Manage your brands and products", "orders": "Orders", "ordersSubtitle": "Manage customer orders"}, "labels": {"yourBrands": "Your brands", "products": "products", "noBrand": "No Brand", "category": "Category", "notSpecified": "Not specified", "filterProducts": "Filter Products", "allProducts": "All Products", "filterByBrand": "Filter by Brand", "filterByCategory": "Filter by Category", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "errors": {"somethingWentWrong": "Something went wrong"}, "productDetails": {"labels": {"price": "Price", "unnamedProduct": "Unnamed Product", "brand": "Brand", "details": "Details", "category": "Category", "created": "Created", "updated": "Updated", "unknown": "Unknown"}, "actions": {"edit": "Edit", "delete": "Delete"}, "modal": {"title": "Delete Product", "message": "Are you sure you want to delete this product? This action cannot be undone.", "confirmText": "Delete", "cancelText": "Cancel"}, "errors": {"noProductFound": "No Product Found"}}, "productForm": {"placeholders": {"productName": "Product name *", "productDescription": "Product description", "selectCategory": "Select category *", "price": "Price *"}}, "brandDetails": {"actions": {"editBrand": "Edit Brand", "deleteBrand": "Delete Brand"}, "sections": {"about": "About", "contact": "Contact", "location": "Location", "products": "Products", "reviews": "Reviews"}, "labels": {"email": "Email", "phone": "Phone", "products": "products", "reviews": "reviews", "noDescription": "No description available for this brand.", "brandLocation": "Brand Location", "noCommentProvided": "No comment provided"}, "modal": {"title": "Delete Brand", "message": "This action will permanently delete the brand and all its associated data. This cannot be undone.", "confirmText": "Delete", "cancelText": "Cancel"}}}, "client": {"shop": {"shopByCategory": "Shop by category"}, "sliders": {"featuredBrands": "Featured Brands", "featuredProducts": "Featured Products", "viewAll": "View All"}, "favorites": {"brands": "Favorite Brands", "products": "Favorite Products"}, "featured": {"label": "Featured", "empty": "No featured items available"}, "cart": {"empty": {"title": "Your cart is empty", "description": "Browse our curated collection of local brands and artisans to find something you love."}}, "brandDetails": {"vendor": "<PERSON><PERSON><PERSON>", "about": "About", "reviews": "Reviews", "noDescription": "No description available", "noDetailedDescription": "No detailed description available for this brand.", "unknownVendor": "Unknown V<PERSON>or", "locationNotSpecified": "Location not specified", "noCommentProvided": "No comment provided"}, "products": {"title": "Products", "titleWithCount": "Products ({{count}})"}, "brands": {"title": "Brands", "titleWithCount": "Brands ({{count}})"}}, "permissions": {"gallery": {"title": "Enable Access to Photos", "description": "To upload photos, <PERSON><PERSON><PERSON> needs access to your photo library. Please enable this in your device settings."}, "notifications": {"title": "Stay in the loop", "description": "Enable notifications to get updates on your orders, new products, and exclusive offers.", "openSettings": "Open Settings", "notNow": "Not Now"}}, "bottomSheets": {"addReview": {"title": "Add a review", "rating": "Rating", "comment": "Comment", "submit": "Submit"}, "login": {"title": "Role Required", "description": "Please log in to continue", "loginButton": "<PERSON><PERSON>", "cancelButton": "Cancel"}, "brands": {"title": "Select Brand", "searchPlaceholder": "Search brands..."}, "categories": {"title": "Select Category", "searchPlaceholder": "Search categories..."}}}